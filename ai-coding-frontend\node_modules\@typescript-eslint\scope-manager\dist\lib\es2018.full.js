"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es2018_full = void 0;
const dom_1 = require("./dom");
const dom_asynciterable_1 = require("./dom.asynciterable");
const dom_iterable_1 = require("./dom.iterable");
const es2018_1 = require("./es2018");
const scripthost_1 = require("./scripthost");
const webworker_importscripts_1 = require("./webworker.importscripts");
exports.es2018_full = {
    libs: [
        es2018_1.es2018,
        dom_1.dom,
        webworker_importscripts_1.webworker_importscripts,
        scripthost_1.scripthost,
        dom_iterable_1.dom_iterable,
        dom_asynciterable_1.dom_asynciterable,
    ],
    variables: [],
};
