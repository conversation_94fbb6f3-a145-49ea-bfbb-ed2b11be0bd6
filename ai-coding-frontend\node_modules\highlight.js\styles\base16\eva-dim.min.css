/*!
  Theme: <PERSON>
  Author: k<PERSON><PERSON><PERSON> (https://github.com/kjakapat)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/pre code.hljs{display:block;overflow-x:auto;padding:1em}code.hljs{padding:3px 5px}.hljs{color:#9fa2a6;background:#2a3b4d}.hljs ::selection,.hljs::selection{background-color:#4b6988;color:#9fa2a6}.hljs-comment{color:#55799c}.hljs-tag{color:#7e90a3}.hljs-operator,.hljs-punctuation,.hljs-subst{color:#9fa2a6}.hljs-operator{opacity:.7}.hljs-bullet,.hljs-deletion,.hljs-name,.hljs-selector-tag,.hljs-template-variable,.hljs-variable{color:#c4676c}.hljs-attr,.hljs-link,.hljs-literal,.hljs-number,.hljs-symbol,.hljs-variable.constant_{color:#f96}.hljs-class .hljs-title,.hljs-title,.hljs-title.class_{color:#cfd05d}.hljs-strong{font-weight:700;color:#cfd05d}.hljs-addition,.hljs-code,.hljs-string,.hljs-title.class_.inherited__{color:#5de561}.hljs-built_in,.hljs-doctag,.hljs-keyword.hljs-atrule,.hljs-quote,.hljs-regexp{color:#4b8f77}.hljs-attribute,.hljs-function .hljs-title,.hljs-section,.hljs-title.function_,.ruby .hljs-property{color:#1ae1dc}.diff .hljs-meta,.hljs-keyword,.hljs-template-tag,.hljs-type{color:#9c6cd3}.hljs-emphasis{color:#9c6cd3;font-style:italic}.hljs-meta,.hljs-meta .hljs-keyword,.hljs-meta .hljs-string{color:#bb64a9}.hljs-meta .hljs-keyword,.hljs-meta-keyword{font-weight:700}