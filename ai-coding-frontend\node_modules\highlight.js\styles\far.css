pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*

FAR Style (c) MajestiC <<EMAIL>>

*/
.hljs {
  color: #0ff;
  background: #000080
}
.hljs-subst {
  /* default */
  
}
.hljs-string,
.hljs-attribute,
.hljs-symbol,
.hljs-bullet,
.hljs-built_in,
.hljs-template-tag,
.hljs-template-variable,
.hljs-addition {
  color: #ff0
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-section,
.hljs-type,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-variable {
  color: #fff
}
.hljs-comment,
.hljs-quote,
.hljs-doctag,
.hljs-deletion {
  color: #888
}
.hljs-number,
.hljs-regexp,
.hljs-literal,
.hljs-link {
  color: #0f0
}
.hljs-meta {
  color: #008080
}
.hljs-keyword,
.hljs-selector-tag,
.hljs-title,
.hljs-section,
.hljs-name,
.hljs-strong {
  font-weight: bold
}
.hljs-emphasis {
  font-style: italic
}