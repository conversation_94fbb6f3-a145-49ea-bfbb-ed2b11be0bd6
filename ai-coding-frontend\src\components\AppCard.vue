<template>
  <div class="app-card" :class="{ 'app-card--featured': featured }">
    <div class="card-glow"></div>
    <div class="app-preview">
      <img v-if="app.cover" :src="app.cover" :alt="app.appName" class="preview-image" />
      <div v-else class="app-placeholder">
        <BotIcon :size="48" class="placeholder-icon" />
      </div>
      <div class="app-overlay">
        <a-space direction="vertical" size="small">
          <a-button type="primary" @click="handleViewChat" class="action-btn primary-btn">
            <MessageCircleIcon :size="16" />
            <span>查看对话</span>
          </a-button>
          <a-button v-if="app.deployKey" @click="handleViewWork" class="action-btn secondary-btn">
            <ExternalLinkIcon :size="16" />
            <span>查看作品</span>
          </a-button>
        </a-space>
      </div>
      <div v-if="featured" class="featured-badge">
        <StarIcon :size="14" />
        <span>精选</span>
      </div>
    </div>
    <div class="app-info">
      <div class="app-info-left">
        <a-avatar :src="app.user?.userAvatar" :size="40" class="user-avatar">
          <template #icon>
            <UserIcon :size="20" />
          </template>
          {{ app.user?.userName?.charAt(0) || 'U' }}
        </a-avatar>
      </div>
      <div class="app-info-right">
        <h3 class="app-title">{{ app.appName || '未命名应用' }}</h3>
        <p class="app-author">
          <UserIcon :size="12" class="author-icon" />
          {{ app.user?.userName || (featured ? '官方' : '未知用户') }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  BotIcon,
  MessageCircleIcon,
  ExternalLinkIcon,
  StarIcon,
  UserIcon
} from 'lucide-vue-next'

interface Props {
  app: API.AppVO
  featured?: boolean
}

interface Emits {
  (e: 'view-chat', appId: string | number | undefined): void
  (e: 'view-work', app: API.AppVO): void
}

const props = withDefaults(defineProps<Props>(), {
  featured: false,
})

const emit = defineEmits<Emits>()

const handleViewChat = () => {
  emit('view-chat', props.app.id)
}

const handleViewWork = () => {
  emit('view-work', props.app)
}
</script>

<style scoped>
.app-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--glass-border);
  transition: all var(--transition-normal);
  cursor: pointer;
  position: relative;
  animation: fadeIn 0.6s ease-out;
}

.app-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--primary-gradient-light);
  opacity: 0;
  transition: opacity var(--transition-normal);
  z-index: 1;
  pointer-events: none;
}

.app-card:hover::before {
  opacity: 1;
}

.app-card:hover {
  transform: translateY(-12px) scale(1.02);
  box-shadow: var(--shadow-2xl);
  border-color: rgba(102, 126, 234, 0.3);
}

.app-card--featured {
  border: 2px solid transparent;
  background: linear-gradient(var(--glass-bg), var(--glass-bg)) padding-box,
              var(--accent-gradient) border-box;
}

.card-glow {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: var(--primary-gradient);
  border-radius: var(--radius-2xl);
  opacity: 0;
  filter: blur(8px);
  transition: opacity var(--transition-normal);
  z-index: -1;
}

.app-card:hover .card-glow {
  opacity: 0.3;
}

.app-card--featured .card-glow {
  background: var(--accent-gradient);
}

.app-preview {
  height: 200px;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  position: relative;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform var(--transition-normal);
}

.app-card:hover .preview-image {
  transform: scale(1.1);
}

.app-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
}

.placeholder-icon {
  color: rgba(102, 126, 234, 0.6);
  animation: float 3s ease-in-out infinite;
}

.app-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.5) 100%);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: all var(--transition-normal);
  z-index: 2;
}

.app-card:hover .app-overlay {
  opacity: 1;
}

.action-btn {
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  min-width: 120px;
  justify-content: center;
}

.primary-btn {
  background: var(--primary-gradient);
  border: none;
  color: white;
  box-shadow: var(--shadow-md);
}

.primary-btn:hover {
  background: var(--primary-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.secondary-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: var(--color-gray-700);
  backdrop-filter: blur(10px);
}

.secondary-btn:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.featured-badge {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--accent-gradient);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  display: flex;
  align-items: center;
  gap: 2px;
  box-shadow: var(--shadow-md);
  z-index: 3;
  animation: pulse 2s infinite;
}

.app-info {
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  background: rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
  position: relative;
  z-index: 2;
}

.app-info-left {
  flex-shrink: 0;
}

.user-avatar {
  border: 2px solid rgba(102, 126, 234, 0.2);
  transition: all var(--transition-normal);
}

.app-card:hover .user-avatar {
  border-color: rgba(102, 126, 234, 0.5);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.app-info-right {
  flex: 1;
  min-width: 0;
}

.app-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  margin: 0 0 var(--spacing-xs);
  color: var(--color-gray-800);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color var(--transition-normal);
}

.app-card:hover .app-title {
  color: #667eea;
}

.app-author {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.author-icon {
  flex-shrink: 0;
  opacity: 0.7;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .app-preview {
    height: 160px;
  }

  .app-info {
    padding: var(--spacing-md);
  }

  .action-btn {
    min-width: 100px;
    font-size: var(--font-size-sm);
  }
}

@media (max-width: 480px) {
  .app-preview {
    height: 140px;
  }

  .app-title {
    font-size: var(--font-size-base);
  }

  .action-btn span {
    display: none;
  }

  .action-btn {
    min-width: auto;
    width: 40px;
    height: 40px;
    border-radius: 50%;
  }
}
</style>
