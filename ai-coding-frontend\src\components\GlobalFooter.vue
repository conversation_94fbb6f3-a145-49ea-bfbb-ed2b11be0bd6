<template>
  <a-layout-footer class="footer">
    <div class="footer-content">
      <div class="footer-info">
        <div class="footer-logo">
          <BotIcon :size="24" class="logo-icon" />
          <span class="logo-text">AI 应用生成平台</span>
        </div>
        <p class="copyright">
          <HeartIcon :size="16" class="heart-icon" />
          <a
            href="https://www.codefather.cn"
            target="_blank"
            rel="noopener noreferrer"
            class="author-link"
          >
            编程导航原创项目 by 程序员鱼皮
          </a>
        </p>
      </div>
    </div>
  </a-layout-footer>
</template>

<script setup lang="ts">
import { BotIcon, HeartIcon } from 'lucide-vue-next'
</script>

<style scoped>
.footer {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-top: 1px solid var(--glass-border);
  text-align: center;
  padding: var(--spacing-xl);
  margin-top: var(--spacing-3xl);
  position: relative;
  z-index: 1;
}

.footer-content {
  max-width: 1200px;
  margin: 0 auto;
}

.footer-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
}

.logo-icon {
  color: #667eea;
  animation: float 3s ease-in-out infinite;
}

.logo-text {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.copyright {
  margin: 0;
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  justify-content: center;
}

.heart-icon {
  color: #ef4444;
  animation: pulse 2s infinite;
}

.author-link {
  color: var(--color-gray-700);
  text-decoration: none;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.author-link:hover {
  color: #667eea;
  text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer {
    padding: var(--spacing-lg);
  }

  .footer-logo {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .logo-text {
    font-size: var(--font-size-base);
  }

  .copyright {
    flex-direction: column;
    gap: var(--spacing-xs);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: var(--spacing-md);
  }

  .copyright {
    font-size: var(--font-size-xs);
  }
}
</style>
