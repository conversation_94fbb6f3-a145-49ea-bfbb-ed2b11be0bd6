<template>
  <a-layout-header class="header">
    <div class="header-background"></div>
    <a-row :wrap="false" class="header-content">
      <!-- 左侧：Logo和标题 -->
      <a-col flex="200px">
        <RouterLink to="/" class="logo-link">
          <div class="header-left">
            <div class="logo-container">
              <img class="logo" src="@/assets/logo.png" alt="Logo" />
              <div class="logo-glow"></div>
            </div>
            <h1 class="site-title">
              <span class="title-gradient">鱼皮应用生成</span>
            </h1>
          </div>
        </RouterLink>
      </a-col>
      <!-- 中间：导航菜单 -->
      <a-col flex="auto">
        <a-menu
          v-model:selectedKeys="selectedKeys"
          mode="horizontal"
          :items="menuItems"
          @click="handleMenuClick"
          class="nav-menu"
        />
      </a-col>
      <!-- 右侧：用户操作区域 -->
      <a-col>
        <div class="user-login-status">
          <div v-if="loginUserStore.loginUser.id" class="user-info">
            <a-dropdown placement="bottomRight">
              <a-space class="user-dropdown">
                <a-avatar
                  :src="loginUserStore.loginUser.userAvatar"
                  class="user-avatar"
                  :size="36"
                >
                  <template #icon>
                    <UserIcon :size="20" />
                  </template>
                </a-avatar>
                <span class="user-name">{{ loginUserStore.loginUser.userName ?? '无名' }}</span>
                <ChevronDownIcon :size="16" class="dropdown-icon" />
              </a-space>
              <template #overlay>
                <a-menu class="user-menu">
                  <a-menu-item @click="doLogout" class="logout-item">
                    <LogOutIcon :size="16" />
                    <span>退出登录</span>
                  </a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
          <div v-else>
            <a-button type="primary" href="/user/login" class="login-btn">
              <LogInIcon :size="16" />
              <span>登录</span>
            </a-button>
          </div>
        </div>
      </a-col>
    </a-row>
  </a-layout-header>
</template>

<script setup lang="ts">
import { computed, h, ref } from 'vue'
import { useRouter } from 'vue-router'
import { type MenuProps, message } from 'ant-design-vue'
import { useLoginUserStore } from '@/stores/loginUser.ts'
import { userLogout } from '@/api/userController.ts'
import { LogoutOutlined, HomeOutlined } from '@ant-design/icons-vue'
import {
  UserIcon,
  LogOutIcon,
  LogInIcon,
  ChevronDownIcon,
  HomeIcon,
  UsersIcon,
  AppWindowIcon,
  ExternalLinkIcon
} from 'lucide-vue-next'

const loginUserStore = useLoginUserStore()
const router = useRouter()
// 当前选中菜单
const selectedKeys = ref<string[]>(['/'])
// 监听路由变化，更新当前选中菜单
router.afterEach((to, from, next) => {
  selectedKeys.value = [to.path]
})

// 菜单配置项
const originItems = [
  {
    key: '/',
    icon: () => h(HomeIcon, { size: 16 }),
    label: '主页',
    title: '主页',
  },
  {
    key: '/admin/userManage',
    icon: () => h(UsersIcon, { size: 16 }),
    label: '用户管理',
    title: '用户管理',
  },
  {
    key: '/admin/appManage',
    icon: () => h(AppWindowIcon, { size: 16 }),
    label: '应用管理',
    title: '应用管理',
  },
  {
    key: 'others',
    icon: () => h(ExternalLinkIcon, { size: 16 }),
    label: h('a', { href: 'https://www.codefather.cn', target: '_blank' }, '编程导航'),
    title: '编程导航',
  },
]

// 过滤菜单项
const filterMenus = (menus = [] as MenuProps['items']) => {
  return menus?.filter((menu) => {
    const menuKey = menu?.key as string
    if (menuKey?.startsWith('/admin')) {
      const loginUser = loginUserStore.loginUser
      if (!loginUser || loginUser.userRole !== 'admin') {
        return false
      }
    }
    return true
  })
}

// 展示在菜单的路由数组
const menuItems = computed<MenuProps['items']>(() => filterMenus(originItems))

// 处理菜单点击
const handleMenuClick: MenuProps['onClick'] = (e) => {
  const key = e.key as string
  selectedKeys.value = [key]
  // 跳转到对应页面
  if (key.startsWith('/')) {
    router.push(key)
  }
}

// 退出登录
const doLogout = async () => {
  const res = await userLogout()
  if (res.data.code === 0) {
    loginUserStore.setLoginUser({
      userName: '未登录',
    })
    message.success('退出登录成功')
    await router.push('/user/login')
  } else {
    message.error('退出登录失败，' + res.data.message)
  }
}
</script>

<style scoped>
.header {
  background: transparent;
  padding: 0;
  height: 64px;
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.header-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border-bottom: 1px solid var(--glass-border);
  z-index: -1;
}

.header-content {
  height: 100%;
  padding: 0 var(--spacing-xl);
  position: relative;
  z-index: 1;
}

.logo-link {
  display: block;
  transition: all var(--transition-normal);
}

.logo-link:hover {
  transform: translateY(-1px);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  height: 100%;
}

.logo-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.logo {
  height: 40px;
  width: 40px;
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  position: relative;
  z-index: 2;
}

.logo-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 50px;
  height: 50px;
  background: var(--primary-gradient);
  border-radius: 50%;
  opacity: 0;
  filter: blur(10px);
  transition: opacity var(--transition-normal);
  z-index: 1;
}

.logo-link:hover .logo-glow {
  opacity: 0.3;
}

.site-title {
  margin: 0;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
}

.title-gradient {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  transition: all var(--transition-normal);
}

.logo-link:hover .title-gradient {
  background: var(--primary-gradient-hover);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 导航菜单样式 */
:deep(.nav-menu) {
  background: transparent;
  border-bottom: none;
  line-height: 64px;
}

:deep(.nav-menu .ant-menu-item) {
  border-bottom: 2px solid transparent;
  margin: 0 var(--spacing-sm);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  transition: all var(--transition-normal);
  color: var(--color-gray-700);
  font-weight: var(--font-weight-medium);
}

:deep(.nav-menu .ant-menu-item:hover) {
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
  border-bottom-color: rgba(102, 126, 234, 0.3);
}

:deep(.nav-menu .ant-menu-item-selected) {
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  border-bottom-color: #667eea;
}

:deep(.nav-menu .ant-menu-item .anticon) {
  margin-right: var(--spacing-xs);
}

/* 用户区域样式 */
.user-login-status {
  height: 100%;
  display: flex;
  align-items: center;
}

.user-info {
  height: 100%;
  display: flex;
  align-items: center;
}

.user-dropdown {
  cursor: pointer;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-lg);
  transition: all var(--transition-normal);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.user-dropdown:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.user-avatar {
  border: 2px solid rgba(102, 126, 234, 0.3);
  transition: all var(--transition-normal);
}

.user-dropdown:hover .user-avatar {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

.user-name {
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin: 0 var(--spacing-xs);
}

.dropdown-icon {
  color: var(--color-gray-500);
  transition: transform var(--transition-normal);
}

.user-dropdown:hover .dropdown-icon {
  transform: rotate(180deg);
}

.login-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-lg);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  box-shadow: var(--shadow-md);
}

.login-btn:hover {
  background: var(--primary-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 用户菜单样式 */
:deep(.user-menu) {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-lg);
  box-shadow: var(--glass-shadow);
  padding: var(--spacing-xs);
}

:deep(.user-menu .ant-menu-item) {
  border-radius: var(--radius-md);
  margin: 2px 0;
  transition: all var(--transition-normal);
}

.logout-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--color-error);
}

:deep(.logout-item:hover) {
  background: rgba(239, 68, 68, 0.1);
  color: var(--color-error);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 var(--spacing-md);
  }

  .site-title {
    display: none;
  }

  .user-name {
    display: none;
  }

  :deep(.nav-menu .ant-menu-item) {
    margin: 0 2px;
    padding: 0 8px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 var(--spacing-sm);
  }

  .logo {
    height: 32px;
    width: 32px;
  }

  .login-btn span {
    display: none;
  }
}
</style>
