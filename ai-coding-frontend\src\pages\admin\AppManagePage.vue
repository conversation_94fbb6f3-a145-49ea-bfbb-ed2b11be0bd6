<template>
  <div id="appManagePage">
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <AppWindowIcon :size="32" />
        </div>
        <div class="header-text">
          <h1 class="page-title">应用管理</h1>
          <p class="page-description">管理系统中的所有AI生成应用</p>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-section">
      <a-form layout="inline" :model="searchParams" @finish="doSearch" class="search-form">
        <a-form-item label="应用名称" class="search-item">
          <a-input
            v-model:value="searchParams.appName"
            placeholder="输入应用名称"
            class="search-input"
          >
            <template #prefix>
              <AppWindowIcon :size="16" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="创建者" class="search-item">
          <a-input
            v-model:value="searchParams.userId"
            placeholder="输入用户ID"
            class="search-input"
          >
            <template #prefix>
              <UserIcon :size="16" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="生成类型" class="search-item">
          <a-select
            v-model:value="searchParams.codeGenType"
            placeholder="选择生成类型"
            style="width: 150px"
            class="search-select"
          >
            <a-select-option value="">全部</a-select-option>
            <a-select-option
              v-for="option in CODE_GEN_TYPE_OPTIONS"
              :key="option.value"
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item class="search-item">
          <a-button type="primary" html-type="submit" class="search-btn">
            <SearchIcon :size="16" />
            <span>搜索</span>
          </a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 表格 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="data"
        :pagination="pagination"
        @change="doTableChange"
        :scroll="{ x: 1200 }"
        class="custom-table"
      >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'cover'">
          <a-image v-if="record.cover" :src="record.cover" :width="80" :height="60" />
          <div v-else class="no-cover">无封面</div>
        </template>
        <template v-else-if="column.dataIndex === 'initPrompt'">
          <a-tooltip :title="record.initPrompt">
            <div class="prompt-text">{{ record.initPrompt }}</div>
          </a-tooltip>
        </template>
        <template v-else-if="column.dataIndex === 'codeGenType'">
          {{ formatCodeGenType(record.codeGenType) }}
        </template>
        <template v-else-if="column.dataIndex === 'priority'">
          <a-tag v-if="record.priority === 99" color="gold">精选</a-tag>
          <span v-else>{{ record.priority || 0 }}</span>
        </template>
        <template v-else-if="column.dataIndex === 'deployedTime'">
          <span v-if="record.deployedTime">
            {{ formatTime(record.deployedTime) }}
          </span>
          <span v-else class="text-gray">未部署</span>
        </template>
        <template v-else-if="column.dataIndex === 'createTime'">
          {{ formatTime(record.createTime) }}
        </template>
        <template v-else-if="column.dataIndex === 'user'">
          <UserInfo :user="record.user" size="small" />
        </template>
        <template v-else-if="column.key === 'action'">
          <a-space>
            <a-button type="primary" size="small" @click="editApp(record)"> 编辑 </a-button>
            <a-button
              type="default"
              size="small"
              @click="toggleFeatured(record)"
              :class="{ 'featured-btn': record.priority === 99 }"
            >
              {{ record.priority === 99 ? '取消精选' : '精选' }}
            </a-button>
            <a-popconfirm title="确定要删除这个应用吗？" @confirm="deleteApp(record.id)">
              <a-button danger size="small" class="delete-btn">
                <TrashIcon :size="14" />
                <span>删除</span>
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </template>
      </a-table>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { listAppVoByPageByAdmin, deleteAppByAdmin, updateAppByAdmin } from '@/api/appController'
import { CODE_GEN_TYPE_OPTIONS, formatCodeGenType } from '@/utils/codeGenTypes'
import { formatTime } from '@/utils/time'
import UserInfo from '@/components/UserInfo.vue'
import {
  AppWindowIcon,
  UserIcon,
  SearchIcon,
  TrashIcon
} from 'lucide-vue-next'

const router = useRouter()

const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80,
    fixed: 'left',
  },
  {
    title: '应用名称',
    dataIndex: 'appName',
    width: 150,
  },
  {
    title: '封面',
    dataIndex: 'cover',
    width: 100,
  },
  {
    title: '初始提示词',
    dataIndex: 'initPrompt',
    width: 200,
  },
  {
    title: '生成类型',
    dataIndex: 'codeGenType',
    width: 100,
  },
  {
    title: '优先级',
    dataIndex: 'priority',
    width: 80,
  },
  {
    title: '部署时间',
    dataIndex: 'deployedTime',
    width: 160,
  },
  {
    title: '创建者',
    dataIndex: 'user',
    width: 120,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
  },
]

// 数据
const data = ref<API.AppVO[]>([])
const total = ref(0)

// 搜索条件
const searchParams = reactive<API.AppQueryRequest>({
  pageNum: 1,
  pageSize: 10,
})

// 获取数据
const fetchData = async () => {
  try {
    const res = await listAppVoByPageByAdmin({
      ...searchParams,
    })
    if (res.data.data) {
      data.value = res.data.data.records ?? []
      total.value = res.data.data.totalRow ?? 0
    } else {
      message.error('获取数据失败，' + res.data.message)
    }
  } catch (error) {
    console.error('获取数据失败：', error)
    message.error('获取数据失败')
  }
}

// 页面加载时请求一次
onMounted(() => {
  fetchData()
})

// 分页参数
const pagination = computed(() => {
  return {
    current: searchParams.pageNum ?? 1,
    pageSize: searchParams.pageSize ?? 10,
    total: total.value,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`,
  }
})

// 表格变化处理
const doTableChange = (page: { current: number; pageSize: number }) => {
  searchParams.pageNum = page.current
  searchParams.pageSize = page.pageSize
  fetchData()
}

// 搜索
const doSearch = () => {
  // 重置页码
  searchParams.pageNum = 1
  fetchData()
}

// 编辑应用
const editApp = (app: API.AppVO) => {
  router.push(`/app/edit/${app.id}`)
}

// 切换精选状态
const toggleFeatured = async (app: API.AppVO) => {
  if (!app.id) return

  const newPriority = app.priority === 99 ? 0 : 99

  try {
    const res = await updateAppByAdmin({
      id: app.id,
      priority: newPriority,
    })

    if (res.data.code === 0) {
      message.success(newPriority === 99 ? '已设为精选' : '已取消精选')
      // 刷新数据
      fetchData()
    } else {
      message.error('操作失败：' + res.data.message)
    }
  } catch (error) {
    console.error('操作失败：', error)
    message.error('操作失败')
  }
}

// 删除应用
const deleteApp = async (id: number | undefined) => {
  if (!id) return

  try {
    const res = await deleteAppByAdmin({ id })
    if (res.data.code === 0) {
      message.success('删除成功')
      // 刷新数据
      fetchData()
    } else {
      message.error('删除失败：' + res.data.message)
    }
  } catch (error) {
    console.error('删除失败：', error)
    message.error('删除失败')
  }
}
</script>

<style scoped>
#appManagePage {
  min-height: 100vh;
  background: transparent;
  padding: var(--spacing-xl);
}

.page-header {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.header-icon {
  width: 64px;
  height: 64px;
  background: var(--secondary-gradient);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-md);
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-xs);
  background: var(--secondary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin: 0;
}

.search-section {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.search-form {
  width: 100%;
}

.search-item {
  margin-bottom: var(--spacing-md);
}

:deep(.search-input) {
  border-radius: var(--radius-lg);
  border: 1px solid rgba(118, 75, 162, 0.2);
  transition: all var(--transition-normal);
}

:deep(.search-input:hover) {
  border-color: rgba(118, 75, 162, 0.4);
}

:deep(.search-input:focus) {
  border-color: #764ba2;
  box-shadow: 0 0 0 2px rgba(118, 75, 162, 0.1);
}

:deep(.search-select .ant-select-selector) {
  border-radius: var(--radius-lg);
  border: 1px solid rgba(118, 75, 162, 0.2);
  transition: all var(--transition-normal);
}

:deep(.search-select:hover .ant-select-selector) {
  border-color: rgba(118, 75, 162, 0.4);
}

:deep(.search-select.ant-select-focused .ant-select-selector) {
  border-color: #764ba2;
  box-shadow: 0 0 0 2px rgba(118, 75, 162, 0.1);
}

.search-btn {
  background: var(--secondary-gradient);
  border: none;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.search-btn:hover {
  background: var(--secondary-gradient-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.table-section {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

:deep(.custom-table) {
  background: transparent;
}

:deep(.custom-table .ant-table) {
  background: transparent;
}

:deep(.custom-table .ant-table-thead > tr > th) {
  background: rgba(118, 75, 162, 0.1);
  border-bottom: 1px solid rgba(118, 75, 162, 0.2);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
}

:deep(.custom-table .ant-table-tbody > tr > td) {
  border-bottom: 1px solid rgba(118, 75, 162, 0.1);
  transition: all var(--transition-normal);
  vertical-align: middle;
}

:deep(.custom-table .ant-table-tbody > tr:hover > td) {
  background: rgba(118, 75, 162, 0.05);
}

:deep(.custom-table .ant-pagination) {
  margin-top: var(--spacing-lg);
}

.no-cover {
  width: 80px;
  height: 60px;
  background: var(--color-gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--color-gray-500);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-md);
  font-style: italic;
}

.prompt-text {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  color: var(--color-gray-700);
}

.text-gray {
  color: var(--color-gray-500);
}

.featured-btn {
  background: var(--accent-gradient);
  border: none;
  border-radius: var(--radius-md);
  color: white;
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.featured-btn:hover {
  background: var(--accent-gradient-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.delete-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.delete-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  #appManagePage {
    padding: var(--spacing-md);
  }

  .page-header {
    padding: var(--spacing-lg);
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .search-section {
    padding: var(--spacing-lg);
  }

  .table-section {
    padding: var(--spacing-lg);
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .header-icon {
    width: 48px;
    height: 48px;
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .search-btn span,
  .delete-btn span {
    display: none;
  }
}
</style>
