<template>
  <div id="userManagePage">
    <div class="page-header">
      <div class="header-content">
        <div class="header-icon">
          <UsersIcon :size="32" />
        </div>
        <div class="header-text">
          <h1 class="page-title">用户管理</h1>
          <p class="page-description">管理系统中的所有用户信息</p>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-section">
      <a-form layout="inline" :model="searchParams" @finish="doSearch" class="search-form">
        <a-form-item label="账号" class="search-item">
          <a-input
            v-model:value="searchParams.userAccount"
            placeholder="输入账号"
            class="search-input"
            prefix-icon
          >
            <template #prefix>
              <UserIcon :size="16" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item label="用户名" class="search-item">
          <a-input
            v-model:value="searchParams.userName"
            placeholder="输入用户名"
            class="search-input"
          >
            <template #prefix>
              <UserIcon :size="16" />
            </template>
          </a-input>
        </a-form-item>
        <a-form-item class="search-item">
          <a-button type="primary" html-type="submit" class="search-btn">
            <SearchIcon :size="16" />
            <span>搜索</span>
          </a-button>
        </a-form-item>
      </a-form>
    </div>

    <!-- 表格 -->
    <div class="table-section">
      <a-table
        :columns="columns"
        :data-source="data"
        :pagination="pagination"
        @change="doTableChange"
        class="custom-table"
      >
      <template #bodyCell="{ column, record }">
        <template v-if="column.dataIndex === 'userAvatar'">
          <a-image :src="record.userAvatar" :width="120" />
        </template>
        <template v-else-if="column.dataIndex === 'userRole'">
          <div v-if="record.userRole === 'admin'">
            <a-tag color="green">管理员</a-tag>
          </div>
          <div v-else>
            <a-tag color="blue">普通用户</a-tag>
          </div>
        </template>
        <template v-else-if="column.dataIndex === 'createTime'">
          {{ dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') }}
        </template>
        <template v-else-if="column.key === 'action'">
          <a-button danger @click="doDelete(record.id)" class="delete-btn">
            <TrashIcon :size="16" />
            <span>删除</span>
          </a-button>
        </template>
      </template>
      </a-table>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { computed, onMounted, reactive, ref } from 'vue'
import { deleteUser, listUserVoByPage } from '@/api/userController.ts'
import { message } from 'ant-design-vue'
import dayjs from 'dayjs'
import {
  UsersIcon,
  UserIcon,
  SearchIcon,
  TrashIcon
} from 'lucide-vue-next'

const columns = [
  {
    title: 'id',
    dataIndex: 'id',
  },
  {
    title: '账号',
    dataIndex: 'userAccount',
  },
  {
    title: '用户名',
    dataIndex: 'userName',
  },
  {
    title: '头像',
    dataIndex: 'userAvatar',
  },
  {
    title: '简介',
    dataIndex: 'userProfile',
  },
  {
    title: '用户角色',
    dataIndex: 'userRole',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '操作',
    key: 'action',
  },
]

// 展示的数据
const data = ref<API.UserVO[]>([])
const total = ref(0)

// 搜索条件
const searchParams = reactive<API.UserQueryRequest>({
  pageNum: 1,
  pageSize: 10,
})

// 获取数据
const fetchData = async () => {
  const res = await listUserVoByPage({
    ...searchParams,
  })
  if (res.data.data) {
    data.value = res.data.data.records ?? []
    total.value = res.data.data.totalRow ?? 0
  } else {
    message.error('获取数据失败，' + res.data.message)
  }
}

// 分页参数
const pagination = computed(() => {
  return {
    current: searchParams.pageNum ?? 1,
    pageSize: searchParams.pageSize ?? 10,
    total: total.value,
    showSizeChanger: true,
    showTotal: (total: number) => `共 ${total} 条`,
  }
})

// 表格分页变化时的操作
const doTableChange = (page: { current: number; pageSize: number }) => {
  searchParams.pageNum = page.current
  searchParams.pageSize = page.pageSize
  fetchData()
}

// 搜索数据
const doSearch = () => {
  // 重置页码
  searchParams.pageNum = 1
  fetchData()
}

// 删除数据
const doDelete = async (id: string) => {
  if (!id) {
    return
  }
  const res = await deleteUser({ id })
  if (res.data.code === 0) {
    message.success('删除成功')
    // 刷新数据
    fetchData()
  } else {
    message.error('删除失败')
  }
}

// 页面加载时请求一次
onMounted(() => {
  fetchData()
})
</script>

<style scoped>
#userManagePage {
  min-height: 100vh;
  background: transparent;
  padding: var(--spacing-xl);
}

.page-header {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-2xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-lg);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.header-icon {
  width: 64px;
  height: 64px;
  background: var(--primary-gradient);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: var(--shadow-md);
}

.header-text {
  flex: 1;
}

.page-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0 0 var(--spacing-xs);
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-description {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin: 0;
}

.search-section {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  margin-bottom: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

.search-form {
  width: 100%;
}

.search-item {
  margin-bottom: var(--spacing-md);
}

:deep(.search-input) {
  border-radius: var(--radius-lg);
  border: 1px solid rgba(102, 126, 234, 0.2);
  transition: all var(--transition-normal);
}

:deep(.search-input:hover) {
  border-color: rgba(102, 126, 234, 0.4);
}

:deep(.search-input:focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

.search-btn {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.search-btn:hover {
  background: var(--primary-gradient-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.table-section {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-xl);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-md);
}

:deep(.custom-table) {
  background: transparent;
}

:deep(.custom-table .ant-table) {
  background: transparent;
}

:deep(.custom-table .ant-table-thead > tr > th) {
  background: rgba(102, 126, 234, 0.1);
  border-bottom: 1px solid rgba(102, 126, 234, 0.2);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
}

:deep(.custom-table .ant-table-tbody > tr > td) {
  border-bottom: 1px solid rgba(102, 126, 234, 0.1);
  transition: all var(--transition-normal);
}

:deep(.custom-table .ant-table-tbody > tr:hover > td) {
  background: rgba(102, 126, 234, 0.05);
}

:deep(.custom-table .ant-pagination) {
  margin-top: var(--spacing-lg);
}

.delete-btn {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  border: none;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-normal);
}

.delete-btn:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 响应式设计 */
@media (max-width: 768px) {
  #userManagePage {
    padding: var(--spacing-md);
  }

  .page-header {
    padding: var(--spacing-lg);
  }

  .header-content {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-md);
  }

  .page-title {
    font-size: var(--font-size-2xl);
  }

  .search-section {
    padding: var(--spacing-lg);
  }

  .table-section {
    padding: var(--spacing-lg);
    overflow-x: auto;
  }
}

@media (max-width: 480px) {
  .header-icon {
    width: 48px;
    height: 48px;
  }

  .page-title {
    font-size: var(--font-size-xl);
  }

  .search-btn span,
  .delete-btn span {
    display: none;
  }
}
</style>
