<template>
  <div id="userLoginPage">
    <div class="login-background">
      <div class="bg-decoration"></div>
    </div>
    <div class="login-container">
      <div class="login-card">
        <div class="login-header">
          <div class="logo-section">
            <div class="logo-wrapper">
              <BotIcon :size="48" class="logo-icon" />
            </div>
            <h1 class="login-title">欢迎回来</h1>
            <p class="login-subtitle">登录您的账户，继续创建精彩应用</p>
          </div>
        </div>

        <div class="login-form-wrapper">
          <a-form
            :model="formState"
            name="basic"
            autocomplete="off"
            @finish="handleSubmit"
            class="login-form"
            layout="vertical"
          >
            <a-form-item
              name="userAccount"
              :rules="[{ required: true, message: '请输入账号' }]"
              class="form-item"
            >
              <div class="input-wrapper">
                <UserIcon :size="20" class="input-icon" />
                <a-input
                  v-model:value="formState.userAccount"
                  placeholder="请输入账号"
                  class="custom-input"
                  size="large"
                />
              </div>
            </a-form-item>

            <a-form-item
              name="userPassword"
              :rules="[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码长度不能小于 6 位' },
              ]"
              class="form-item"
            >
              <div class="input-wrapper">
                <LockIcon :size="20" class="input-icon" />
                <a-input-password
                  v-model:value="formState.userPassword"
                  placeholder="请输入密码"
                  class="custom-input"
                  size="large"
                />
              </div>
            </a-form-item>

            <div class="form-tips">
              <span>没有账号？</span>
              <RouterLink to="/user/register" class="register-link">
                立即注册
                <ArrowRightIcon :size="14" />
              </RouterLink>
            </div>

            <a-form-item class="submit-item">
              <a-button
                type="primary"
                html-type="submit"
                class="login-button"
                size="large"
                block
              >
                <LogInIcon :size="20" />
                <span>登录</span>
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { reactive } from 'vue'
import { userLogin } from '@/api/userController.ts'
import { useLoginUserStore } from '@/stores/loginUser.ts'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  BotIcon,
  UserIcon,
  LockIcon,
  LogInIcon,
  ArrowRightIcon
} from 'lucide-vue-next'

const formState = reactive<API.UserLoginRequest>({
  userAccount: '',
  userPassword: '',
})

const router = useRouter()
const loginUserStore = useLoginUserStore()

/**
 * 提交表单
 * @param values
 */
const handleSubmit = async (values: any) => {
  const res = await userLogin(values)
  // 登录成功，把登录态保存到全局状态中
  if (res.data.code === 0 && res.data.data) {
    await loginUserStore.fetchLoginUser()
    message.success('登录成功')
    router.push({
      path: '/',
      replace: true,
    })
  } else {
    message.error('登录失败，' + res.data.message)
  }
}
</script>

<style scoped>
#userLoginPage {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.login-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    var(--bg-gradient-primary),
    radial-gradient(circle at 30% 20%, rgba(102, 126, 234, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(118, 75, 162, 0.12) 0%, transparent 50%);
  z-index: -2;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(102, 126, 234, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(102, 126, 234, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridMove 20s ease-in-out infinite;
  z-index: -1;
}

.login-container {
  width: 100%;
  max-width: 420px;
  z-index: 1;
}

.login-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  overflow: hidden;
  animation: fadeIn 0.8s ease-out;
}

.login-header {
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-xl);
  text-align: center;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--primary-gradient);
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
  animation: float 3s ease-in-out infinite;
}

.logo-wrapper::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: var(--primary-gradient);
  border-radius: 50%;
  opacity: 0.3;
  filter: blur(8px);
  z-index: -1;
}

.logo-icon {
  color: white;
}

.login-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin: 0;
  line-height: 1.5;
}

.login-form-wrapper {
  padding: 0 var(--spacing-2xl) var(--spacing-2xl);
}

.login-form {
  width: 100%;
}

.form-item {
  margin-bottom: var(--spacing-lg);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--spacing-md);
  color: var(--color-gray-500);
  z-index: 2;
  transition: color var(--transition-normal);
}

:deep(.custom-input) {
  padding-left: 48px;
  border-radius: var(--radius-lg);
  border: 1px solid rgba(102, 126, 234, 0.2);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

:deep(.custom-input:hover) {
  border-color: rgba(102, 126, 234, 0.4);
  background: rgba(255, 255, 255, 0.9);
}

:deep(.custom-input:focus) {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

:deep(.custom-input:focus) + .input-icon,
:deep(.custom-input:hover) + .input-icon {
  color: #667eea;
}

.form-tips {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.register-link {
  color: #667eea;
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 2px;
  transition: all var(--transition-normal);
}

.register-link:hover {
  color: #5a6fd8;
  transform: translateX(2px);
}

.submit-item {
  margin-bottom: 0;
}

.login-button {
  background: var(--primary-gradient);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.login-button:hover {
  background: var(--primary-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.login-button:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 480px) {
  #userLoginPage {
    padding: var(--spacing-md);
  }

  .login-header {
    padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  }

  .login-form-wrapper {
    padding: 0 var(--spacing-lg) var(--spacing-xl);
  }

  .logo-wrapper {
    width: 60px;
    height: 60px;
  }

  .login-title {
    font-size: var(--font-size-2xl);
  }

  .login-subtitle {
    font-size: var(--font-size-sm);
  }
}
</style>
