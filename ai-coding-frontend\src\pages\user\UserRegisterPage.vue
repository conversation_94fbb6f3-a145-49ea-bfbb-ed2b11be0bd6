<template>
  <div id="userRegisterPage">
    <div class="register-background">
      <div class="bg-decoration"></div>
    </div>
    <div class="register-container">
      <div class="register-card">
        <div class="register-header">
          <div class="logo-section">
            <div class="logo-wrapper">
              <UserPlusIcon :size="48" class="logo-icon" />
            </div>
            <h1 class="register-title">创建账户</h1>
            <p class="register-subtitle">加入我们，开始您的AI应用创作之旅</p>
          </div>
        </div>

        <div class="register-form-wrapper">
          <a-form
            :model="formState"
            name="basic"
            autocomplete="off"
            @finish="handleSubmit"
            class="register-form"
            layout="vertical"
          >
            <a-form-item
              name="userAccount"
              :rules="[{ required: true, message: '请输入账号' }]"
              class="form-item"
            >
              <div class="input-wrapper">
                <UserIcon :size="20" class="input-icon" />
                <a-input
                  v-model:value="formState.userAccount"
                  placeholder="请输入账号"
                  class="custom-input"
                  size="large"
                />
              </div>
            </a-form-item>

            <a-form-item
              name="userPassword"
              :rules="[
                { required: true, message: '请输入密码' },
                { min: 6, message: '密码不能小于 6 位' },
              ]"
              class="form-item"
            >
              <div class="input-wrapper">
                <LockIcon :size="20" class="input-icon" />
                <a-input-password
                  v-model:value="formState.userPassword"
                  placeholder="请输入密码"
                  class="custom-input"
                  size="large"
                />
              </div>
            </a-form-item>

            <a-form-item
              name="checkPassword"
              :rules="[
                { required: true, message: '请确认密码' },
                { min: 6, message: '密码不能小于 6 位' },
                { validator: validateCheckPassword },
              ]"
              class="form-item"
            >
              <div class="input-wrapper">
                <ShieldCheckIcon :size="20" class="input-icon" />
                <a-input-password
                  v-model:value="formState.checkPassword"
                  placeholder="请确认密码"
                  class="custom-input"
                  size="large"
                />
              </div>
            </a-form-item>

            <div class="form-tips">
              <span>已有账号？</span>
              <RouterLink to="/user/login" class="login-link">
                立即登录
                <ArrowRightIcon :size="14" />
              </RouterLink>
            </div>

            <a-form-item class="submit-item">
              <a-button
                type="primary"
                html-type="submit"
                class="register-button"
                size="large"
                block
              >
                <UserPlusIcon :size="20" />
                <span>注册</span>
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { userRegister } from '@/api/userController.ts'
import { message } from 'ant-design-vue'
import { reactive } from 'vue'
import {
  UserPlusIcon,
  UserIcon,
  LockIcon,
  ShieldCheckIcon,
  ArrowRightIcon
} from 'lucide-vue-next'

const router = useRouter()

const formState = reactive<API.UserRegisterRequest>({
  userAccount: '',
  userPassword: '',
  checkPassword: '',
})

/**
 * 验证确认密码
 * @param rule
 * @param value
 * @param callback
 */
const validateCheckPassword = (rule: unknown, value: string, callback: (error?: Error) => void) => {
  if (value && value !== formState.userPassword) {
    callback(new Error('两次输入密码不一致'))
  } else {
    callback()
  }
}

/**
 * 提交表单
 * @param values
 */
const handleSubmit = async (values: API.UserRegisterRequest) => {
  const res = await userRegister(values)
  // 注册成功，跳转到登录页面
  if (res.data.code === 0) {
    message.success('注册成功')
    router.push({
      path: '/user/login',
      replace: true,
    })
  } else {
    message.error('注册失败，' + res.data.message)
  }
}
</script>

<style scoped>
#userRegisterPage {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
  position: relative;
  overflow: hidden;
}

.register-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    var(--bg-gradient-primary),
    radial-gradient(circle at 70% 30%, rgba(118, 75, 162, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 30% 70%, rgba(102, 126, 234, 0.12) 0%, transparent 50%);
  z-index: -2;
}

.bg-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(118, 75, 162, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(118, 75, 162, 0.05) 1px, transparent 1px);
  background-size: 60px 60px;
  animation: gridMove 20s ease-in-out infinite reverse;
  z-index: -1;
}

.register-container {
  width: 100%;
  max-width: 440px;
  z-index: 1;
}

.register-card {
  background: var(--glass-bg);
  backdrop-filter: var(--glass-backdrop);
  border: 1px solid var(--glass-border);
  border-radius: var(--radius-2xl);
  box-shadow: var(--glass-shadow);
  overflow: hidden;
  animation: fadeIn 0.8s ease-out;
}

.register-header {
  padding: var(--spacing-2xl) var(--spacing-2xl) var(--spacing-xl);
  text-align: center;
  background: linear-gradient(135deg, rgba(118, 75, 162, 0.05) 0%, rgba(102, 126, 234, 0.05) 100%);
}

.logo-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
}

.logo-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  background: var(--secondary-gradient);
  border-radius: 50%;
  box-shadow: var(--shadow-lg);
  animation: float 3s ease-in-out infinite;
}

.logo-wrapper::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: var(--secondary-gradient);
  border-radius: 50%;
  opacity: 0.3;
  filter: blur(8px);
  z-index: -1;
}

.logo-icon {
  color: white;
}

.register-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  margin: 0;
  background: var(--secondary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.register-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin: 0;
  line-height: 1.5;
}

.register-form-wrapper {
  padding: 0 var(--spacing-2xl) var(--spacing-2xl);
}

.register-form {
  width: 100%;
}

.form-item {
  margin-bottom: var(--spacing-lg);
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: var(--spacing-md);
  color: var(--color-gray-500);
  z-index: 2;
  transition: color var(--transition-normal);
}

:deep(.custom-input) {
  padding-left: 48px;
  border-radius: var(--radius-lg);
  border: 1px solid rgba(118, 75, 162, 0.2);
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  transition: all var(--transition-normal);
}

:deep(.custom-input:hover) {
  border-color: rgba(118, 75, 162, 0.4);
  background: rgba(255, 255, 255, 0.9);
}

:deep(.custom-input:focus) {
  border-color: #764ba2;
  background: white;
  box-shadow: 0 0 0 3px rgba(118, 75, 162, 0.1);
}

:deep(.custom-input:focus) + .input-icon,
:deep(.custom-input:hover) + .input-icon {
  color: #764ba2;
}

.form-tips {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
}

.login-link {
  color: #764ba2;
  font-weight: var(--font-weight-medium);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 2px;
  transition: all var(--transition-normal);
}

.login-link:hover {
  color: #6a4190;
  transform: translateX(2px);
}

.submit-item {
  margin-bottom: 0;
}

.register-button {
  background: var(--secondary-gradient);
  border: none;
  border-radius: var(--radius-lg);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
}

.register-button:hover {
  background: var(--secondary-gradient-hover);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.register-button:active {
  transform: translateY(0);
}

/* 响应式设计 */
@media (max-width: 480px) {
  #userRegisterPage {
    padding: var(--spacing-md);
  }

  .register-header {
    padding: var(--spacing-xl) var(--spacing-lg) var(--spacing-lg);
  }

  .register-form-wrapper {
    padding: 0 var(--spacing-lg) var(--spacing-xl);
  }

  .logo-wrapper {
    width: 60px;
    height: 60px;
  }

  .register-title {
    font-size: var(--font-size-2xl);
  }

  .register-subtitle {
    font-size: var(--font-size-sm);
  }
}
</style>
